# ClassDefinition + ASTUtil Integration Examples

## 1. Property Name Resolution with `AstUtil.resolveName()`

**Most Common Usage**: Extracting string identifiers from AST nodes

```java
// From ClassDefinition.processObjLiteral()
for (ObjectProperty prop : obj.getElements()) {
    BaseNode nameNode = prop.getLeft();
    _currentPropertyName = AstUtil.resolveName(nameNode);  // ← Key ASTUtil call
    
    if ("extend".equals(_currentPropertyName)) {
        _extendName = AstUtil.resolveName(prop.getRight());  // ← Extract parent class
        addReference(_extendName, ReferenceType.ClassExtend, prop.getRight());
        _classRequires.add(_extendName);
    }
}
```

**Example Input JavaScript:**
```javascript
Ext.define('MyApp.view.UserPanel', {
    extend: 'Ext.panel.Panel',  // ← AstUtil.resolveName extracts "Ext.panel.Panel"
    alias: 'widget.userpanel',   // ← AstUtil.resolveName extracts "widget.userpanel"
    requires: ['MyApp.model.User'] // ← AstUtil.resolveName extracts "MyApp.model.User"
});
```

**What AstUtil.resolveName() does:**
- Takes a `BaseNode` (could be Name, StringLiteral, PropertyGet, etc.)
- Uses the `NameVisitor` pattern to traverse the node structure
- Returns the resolved string identifier

## 2. Safe Type Casting with `AstUtil.cast()`

**Usage**: Ensuring AST nodes are of expected types with error handling

```java
// From ClassDefinition.processObjLiteral()
} else if ("override".equals(_currentPropertyName)) {
    _overrideNode = prop.getRight();
    _overrideName = AstUtil.cast(_overrideNode, StringLiteral.class).getValue();  // ← Safe cast
    _isOverride = true;
}
```

**Example Input JavaScript:**
```javascript
Ext.define('MyApp.overrides.grid.Panel', {
    override: 'Ext.grid.Panel',  // ← Must be StringLiteral, AstUtil.cast() validates this
    someMethod: function() {
        // override implementation
    }
});
```

**What AstUtil.cast() does:**
- Validates the node is of the expected type (StringLiteral)
- Throws descriptive ExParse exception if wrong type
- Logs compiler error with location information
- Returns safely cast object

## 3. Object Property Extraction with `AstUtil.getObjectProperties()`

**Usage**: Converting ObjectLiteral AST nodes to Map<String, BaseNode> for easier processing

```java
// From ClassDefinition.getUnderrides()
ClassMember mixConfig = _members.get("mixinConfig");
if(this.isInstanceOf("Ext.Mixin") && mixConfig != null) {
    BaseNode value = mixConfig.getNode();
    if(value instanceof ObjectLiteral) {
        Map<String, BaseNode> props = AstUtil.getObjectProperties((ObjectLiteral)value);  // ← Convert to Map
        BaseNode on = props.get("on");
        if(on != null && on instanceof ObjectLiteral) {
            Map<String, BaseNode> hooks = AstUtil.getObjectProperties((ObjectLiteral)on);  // ← Nested conversion
            underrides.addAll(hooks.keySet());
        }
    }
}
```

**Example Input JavaScript:**
```javascript
Ext.define('MyApp.mixin.Observable', {
    extend: 'Ext.Mixin',
    mixinConfig: {
        on: {
            beforeDestroy: 'onBeforeDestroy',  // ← These become underrides
            afterRender: 'onAfterRender'       // ← These become underrides
        }
    }
});
```

**What AstUtil.getObjectProperties() does:**
- Takes an ObjectLiteral AST node
- Iterates through ObjectProperty elements
- Uses AstUtil.resolveName() on each property name
- Returns Map<String, BaseNode> for easy access

## 4. Complex Property Processing Example

**Real-world processing of mixins property:**

```java
// From ClassDefinition.initMixins()
public void initMixins(ObjectProperty mixins) {
    if (mixins != null) {
        BaseNode mixin = mixins.getRight();
        if (mixin instanceof ArrayLiteral) {
            // Array form: mixins: ['Mixin1', 'Mixin2']
            for (BaseNode el : ((ArrayLiteral) mixin).getElements()) {
                String mname = null;
                if(el instanceof StringLiteral) {
                    mname = ((StringLiteral) el).getValue();
                } else if(el instanceof PropertyGet) {
                    mname = resolveName(el);  // ← Handle complex expressions
                }
                if(mname != null) {
                    _classMixins.add(mname);
                    addReference(mname, ReferenceType.ClassMixin, el);
                }
            }
        } else if(mixin instanceof ObjectLiteral) {
            // Object form: mixins: { observable: 'Ext.util.Observable' }
            _namedMixins = true;
            for (ObjectProperty el : ((ObjectLiteral) mixin).getElements()) {
                String mname = AstUtil.resolveName(el.getRight());  // ← Extract mixin class name
                _classMixins.add(mname);
                addReference(mname, ReferenceType.ClassMixin, el.getRight());
            }
        }
    }
}
```

**Example Input JavaScript:**
```javascript
// Array form
Ext.define('MyApp.Component', {
    mixins: ['Ext.util.Observable', 'MyApp.mixin.Configurable']
});

// Object form  
Ext.define('MyApp.Component', {
    mixins: {
        observable: 'Ext.util.Observable',
        configurable: 'MyApp.mixin.Configurable'
    }
});

// Complex expression form
Ext.define('MyApp.Component', {
    mixins: [MyApp.mixins.SomeMixin]  // ← PropertyGet, needs resolveName()
});
```

## 5. Build Process Integration Flow

```
1. JavaScript Source File
   ↓
2. AstUtil.parse() → Creates RootNode AST
   ↓  
3. ClassDefinition Constructor → Receives BaseNode config
   ↓
4. processConfig() → Determines if ObjectLiteral or FunctionCall
   ↓
5. processObjLiteral() → Iterates through properties
   ↓
6. AstUtil.resolveName() → Extracts property names ("extend", "mixins", etc.)
   ↓
7. AstUtil.cast() → Safely casts values to expected types
   ↓
8. AstUtil.getObjectProperties() → Converts objects to maps
   ↓
9. Build Metadata Generated:
   - _extendName (parent class)
   - _classMixins (mixin dependencies)  
   - _classRequires (explicit dependencies)
   - _classAliases (component aliases)
   - _members (methods and properties)
   - _configs (configuration properties)
```

## 6. Critical Role in Build Optimization

**The ClassDefinition + ASTUtil combination enables:**

### Dependency Analysis
```java
// ClassDefinition tracks all dependencies discovered via ASTUtil
private List<String> _classRequires = new ArrayList<String>();  // from requires: []
private Set<String> _classMixins = new LinkedHashSet<String>(); // from mixins: []
private String _extendName;  // from extend: 'ParentClass'
```

### Class Hierarchy Construction
```java
// Used by build system to determine load order
public synchronized List<ClassDefinition> getAllBaseClasses() {
    if(_allBases == null) {
        List<ClassDefinition> bases = new ArrayList<ClassDefinition>();
        for(ClassDefinition mixin : CollectionUtil.wrap(getMixedInClasses()).reverse()) {
            bases.add(mixin);  // ← Mixins first
        }
        if(getBaseClass() != null) {
            bases.add(getBaseClass());  // ← Parent class last
        }
        _allBases = bases;
    }
    return _allBases;
}
```

### Reference Tracking for Optimization
```java
// Every class reference gets tracked for optimization
private Reference addReference(String name, ReferenceType type, BaseNode node) {
    SourceFile sf = getSourceFile();
    if(sf != null) {
        Reference dr = new Reference(name, type, sf, node);
        _symbols.addReference(dr);  // ← Tracked for optimization
        return dr;
    }
    return null;
}
```

## Summary

ClassDefinition acts as the **semantic layer** that understands Sencha's class system, while ASTUtil provides the **syntactic tools** to extract information from JavaScript AST nodes. Together they:

1. **Parse class definitions** into structured metadata
2. **Track dependencies** for build ordering
3. **Enable optimizations** like reference rewriting
4. **Support modern JavaScript** through ASTUtil's dual-parser system
5. **Provide error handling** with detailed location information

This integration is fundamental to Sencha Command's ability to understand, optimize, and build Sencha applications efficiently.